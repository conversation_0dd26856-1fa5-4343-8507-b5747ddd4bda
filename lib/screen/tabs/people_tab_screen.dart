import 'package:flutter/material.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';

class PeopleTabScreen extends StatefulWidget {
  const PeopleTabScreen({super.key});

  @override
  State<PeopleTabScreen> createState() => _PeopleTabScreenState();
}

class _PeopleTabScreenState extends State<PeopleTabScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'People',
                    style: headlineMedium.copyWith(
                      color: context.themeData.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {
                      // TODO: Filter functionality
                    },
                    icon: Icon(
                      Icons.filter_list,
                      color: context.themeData.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Search bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: context.themeData.neutral100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search,
                    color: context.themeData.textSecondary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search people...',
                        hintStyle: bodyMedium.copyWith(
                          color: context.themeData.textSecondary,
                        ),
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Categories
            Container(
              height: 40,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildCategoryChip('All', true),
                  const SizedBox(width: 8),
                  _buildCategoryChip('Nearby', false),
                  const SizedBox(width: 8),
                  _buildCategoryChip('Online', false),
                  const SizedBox(width: 8),
                  _buildCategoryChip('Suggested', false),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // People list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _mockPeople.length,
                itemBuilder: (context, index) {
                  final person = _mockPeople[index];
                  return _buildPersonItem(person);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String label, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected 
            ? const Color(0xFF6C4EFF) 
            : const Color(0xFFF6F6F6),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        label,
        style: labelMedium.copyWith(
          color: isSelected 
              ? Colors.white 
              : const Color(0xFF777777),
        ),
      ),
    );
  }

  Widget _buildPersonItem(MockPerson person) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFF0F0F0),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: person.avatarColor,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Center(
              child: Text(
                person.initials,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          
          // Person info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  person.name,
                  style: titleMedium.copyWith(
                    color: const Color(0xFF292929),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  person.bio,
                  style: bodySmall.copyWith(
                    color: const Color(0xFF777777),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${person.mutualFriends} mutual friends',
                  style: bodySmall.copyWith(
                    color: const Color(0xFF6C4EFF),
                  ),
                ),
              ],
            ),
          ),
          
          // Connect button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF6C4EFF),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Connect',
              style: labelSmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Mock data for demonstration
class MockPerson {
  final String name;
  final String bio;
  final int mutualFriends;
  final Color avatarColor;
  final String initials;

  MockPerson({
    required this.name,
    required this.bio,
    required this.mutualFriends,
    required this.avatarColor,
    required this.initials,
  });
}

final List<MockPerson> _mockPeople = [
  MockPerson(
    name: 'Sarah Connor',
    bio: 'Tech enthusiast and coffee lover',
    mutualFriends: 5,
    avatarColor: Colors.blue,
    initials: 'SC',
  ),
  MockPerson(
    name: 'John Doe',
    bio: 'Designer at XYZ Company',
    mutualFriends: 12,
    avatarColor: Colors.green,
    initials: 'JD',
  ),
  MockPerson(
    name: 'Jane Smith',
    bio: 'Photographer and traveler',
    mutualFriends: 3,
    avatarColor: Colors.purple,
    initials: 'JS',
  ),
  MockPerson(
    name: 'Mike Johnson',
    bio: 'Software engineer',
    mutualFriends: 8,
    avatarColor: Colors.orange,
    initials: 'MJ',
  ),
  MockPerson(
    name: 'Lisa Wang',
    bio: 'Product manager',
    mutualFriends: 15,
    avatarColor: Colors.pink,
    initials: 'LW',
  ),
];
