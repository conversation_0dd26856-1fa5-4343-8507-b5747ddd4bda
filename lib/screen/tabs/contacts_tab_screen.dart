import 'package:flutter/material.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';

class ContactsTabScreen extends StatefulWidget {
  const ContactsTabScreen({super.key});

  @override
  State<ContactsTabScreen> createState() => _ContactsTabScreenState();
}

class _ContactsTabScreenState extends State<ContactsTabScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'Contacts',
                    style: headlineMedium.copyWith(
                      color: context.themeData.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {
                      // TODO: Add contact functionality
                    },
                    icon: Icon(
                      Icons.person_add,
                      color: context.themeData.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Search bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: context.themeData.neutral100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search,
                    color: context.themeData.textSecondary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search contacts...',
                        hintStyle: bodyMedium.copyWith(
                          color: context.themeData.textSecondary,
                        ),
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Contacts list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _mockContacts.length,
                itemBuilder: (context, index) {
                  final contact = _mockContacts[index];
                  return _buildContactItem(contact);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(MockContact contact) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFF0F0F0),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: contact.avatarColor,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Center(
              child: Text(
                contact.initials,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          
          // Contact info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  contact.name,
                  style: titleMedium.copyWith(
                    color: const Color(0xFF292929),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  contact.status,
                  style: bodySmall.copyWith(
                    color: const Color(0xFF777777),
                  ),
                ),
              ],
            ),
          ),
          
          // Status indicator
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: contact.isOnline ? Colors.green : Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }
}

// Mock data for demonstration
class MockContact {
  final String name;
  final String status;
  final bool isOnline;
  final Color avatarColor;
  final String initials;

  MockContact({
    required this.name,
    required this.status,
    required this.isOnline,
    required this.avatarColor,
    required this.initials,
  });
}

final List<MockContact> _mockContacts = [
  MockContact(
    name: 'Alice Johnson',
    status: 'Available',
    isOnline: true,
    avatarColor: Colors.blue,
    initials: 'AJ',
  ),
  MockContact(
    name: 'Bob Smith',
    status: 'Away',
    isOnline: false,
    avatarColor: Colors.green,
    initials: 'BS',
  ),
  MockContact(
    name: 'Carol Davis',
    status: 'Busy',
    isOnline: true,
    avatarColor: Colors.purple,
    initials: 'CD',
  ),
  MockContact(
    name: 'David Wilson',
    status: 'Offline',
    isOnline: false,
    avatarColor: Colors.orange,
    initials: 'DW',
  ),
  MockContact(
    name: 'Emma Brown',
    status: 'Available',
    isOnline: true,
    avatarColor: Colors.pink,
    initials: 'EB',
  ),
];
