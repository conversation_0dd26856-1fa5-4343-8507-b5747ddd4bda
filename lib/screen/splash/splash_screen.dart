import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/app_settings/app_settings_cubit.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/utils/keychain/keychain_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();

    _startSplashScreen();
  }

  Future<void> _startSplashScreen() async {
    await Future.delayed(const Duration(seconds: 1));
    await _onHandleCheckUserLogin();
  }

  Future<void> _onHandleCheckUserLogin() async {
    // final sharedPrefs = GetIt.instance<SharedPreferencesManager>();

    final isFirstLaunch = await KeychainService.instance.isFirstLaunch();
    
    if (isFirstLaunch == true) {
      context.go(RouterEnums.onboarding.routeName);
    } else {
      context.go(RouterEnums.inital.routeName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          GetIt.instance<AppSettingsCubit>(),
      child: Scaffold(
        backgroundColor: Colors.white,
        body:   const Center(
              child: Column(
                mainAxisAlignment:
                    MainAxisAlignment.center, // Center the content
                children: [],
              ),
            )
          
        
      ),
    );
  }

 
}
